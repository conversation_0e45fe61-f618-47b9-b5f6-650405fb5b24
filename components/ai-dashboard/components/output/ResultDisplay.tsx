"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import {
  Brain,
  CheckCircle,
  AlertCircle,
  Loader2,
  Eye,
  Download,
  Coins
} from "lucide-react";
import { useTranslations } from "next-intl";
import { ImagePreviewModal } from "./ImagePreviewModal";
import { downloadFileViaProxy } from "@/lib/download-utils";
import type { WorkspaceGenerationResult } from "../types";

interface ResultDisplayProps {
  generationResult: WorkspaceGenerationResult | null;
}

export function ResultDisplay({ generationResult }: ResultDisplayProps) {
  const t = useTranslations("ai-dashboard");

  // 图片预览状态
  const [previewImage, setPreviewImage] = useState<{
    url: string;
    alt: string;
  } | null>(null);

  // 下载状态
  const [downloadingImages, setDownloadingImages] = useState<Set<string>>(new Set());
  const [downloadingVideo, setDownloadingVideo] = useState(false);

  // 处理图片预览
  const handleImagePreview = (imageUrl: string, imageAlt: string) => {
    setPreviewImage({ url: imageUrl, alt: imageAlt });
  };

  // 处理图片下载
  const handleImageDownload = async (imageUrl: string, index?: number) => {
    setDownloadingImages(prev => new Set(prev).add(imageUrl));

    try {
      const fileName = `generated-image${index !== undefined ? `-${index + 1}` : ''}.jpg`;

      // 直接使用代理下载，避免 CORS 问题
      const result = await downloadFileViaProxy(imageUrl, fileName);

      if (result.success) {
        toast.success('图片下载成功');
      } else {
        toast.error(`下载失败: ${result.error}`);
      }
    } catch (error) {
      toast.error('下载失败，请重试');
    } finally {
      setDownloadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(imageUrl);
        return newSet;
      });
    }
  };

  // 处理视频下载
  const handleVideoDownload = async (videoUrl: string) => {
    setDownloadingVideo(true);

    try {
      const fileName = 'generated-video.mp4';

      // 直接使用代理下载，避免 CORS 问题
      const result = await downloadFileViaProxy(videoUrl, fileName);

      if (result.success) {
        toast.success('视频下载成功');
      } else {
        toast.error(`下载失败: ${result.error}`);
      }
    } catch (error) {
      toast.error('下载失败，请重试');
    } finally {
      setDownloadingVideo(false);
    }
  };

  if (!generationResult) {
    return (
      <div className="text-center py-12">
        <div className="p-6 rounded-2xl bg-gradient-to-br from-muted/30 to-muted/10 border border-border/30 max-w-md mx-auto">
          <div className="p-4 rounded-xl bg-gradient-to-r from-primary to-accent mb-4 w-fit mx-auto">
            <Brain className="w-8 h-8 text-primary-foreground" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">{t("workspace.start_create")}</h3>
          <p className="text-muted-foreground text-sm">
            {t("workspace.choose_model")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 生成状态 */}
      <div className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-muted/30 to-muted/10 border border-border/30">
        <div className={`p-2 rounded-lg ${
          generationResult.status === 'success' ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
          generationResult.status === 'failed' ? 'bg-gradient-to-r from-red-500 to-rose-500' :
          'bg-gradient-to-r from-blue-500 to-purple-500'
        }`}>
          {generationResult.status === 'success' && <CheckCircle className="w-5 h-5 text-white" />}
          {generationResult.status === 'failed' && <AlertCircle className="w-5 h-5 text-white" />}
          {(generationResult.status === 'pending' || generationResult.status === 'running') && <Loader2 className="w-5 h-5 animate-spin text-white" />}
        </div>
        <div>
          <span className="font-semibold text-foreground">
            {generationResult.status === 'success' && '生成完成'}
            {generationResult.status === 'failed' && '生成失败'}
            {(generationResult.status === 'pending' || generationResult.status === 'running') && '生成中...'}
          </span>
          {generationResult.progress !== undefined && (
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">进度: {generationResult.progress}%</div>
              <Progress value={generationResult.progress} className="w-full" />
            </div>
          )}
        </div>
      </div>

      {/* 结果内容 */}
      {generationResult.status === 'success' && generationResult.result && (
        <div className="space-y-4">
          {/* 文本结果 */}
          {generationResult.result.text && (
            <div className="p-6 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30 backdrop-blur-sm">
              <pre className="whitespace-pre-wrap text-sm leading-relaxed text-foreground">{generationResult.result.text}</pre>
            </div>
          )}

          {/* 图像结果 */}
          {generationResult.result.images && (
            <div className="grid grid-cols-1 gap-4">
              {generationResult.result.images.map((image, index) => {
                const isDownloading = downloadingImages.has(image.url);
                const imageAlt = `Generated image ${index + 1}`;

                return (
                  <div key={index} className="space-y-4 p-4 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30">
                    <div
                      className="relative cursor-pointer group"
                      onClick={() => handleImagePreview(image.url, imageAlt)}
                    >
                      <img
                        src={image.url}
                        alt={imageAlt}
                        className="w-full rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 group-hover:scale-[1.02]"
                      />
                      {/* 预览提示覆盖层 */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 rounded-xl flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 rounded-full p-3">
                          <Eye className="w-6 h-6 text-gray-800" />
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => handleImagePreview(image.url, imageAlt)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        {t("actions.view")}
                      </Button>
                      <Button
                        size="sm"
                        variant="default"
                        className="flex-1"
                        onClick={() => handleImageDownload(image.url, index)}
                        disabled={isDownloading}
                      >
                        {isDownloading ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Download className="w-4 h-4 mr-2" />
                        )}
                        {isDownloading ? '下载中...' : t("actions.download")}
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* 视频结果 */}
          {generationResult.result.video && (
            <div className="space-y-4 p-4 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30">
              <video
                src={generationResult.result.video.url}
                controls
                className="w-full rounded-xl shadow-lg"
              />
              <div className="flex gap-3">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => window.open(generationResult.result?.video?.url, '_blank')}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  在新窗口查看
                </Button>
                <Button
                  size="sm"
                  variant="default"
                  className="flex-1"
                  onClick={() => generationResult.result?.video?.url && handleVideoDownload(generationResult.result.video.url)}
                  disabled={downloadingVideo}
                >
                  {downloadingVideo ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Download className="w-4 h-4 mr-2" />
                  )}
                  {downloadingVideo ? '下载中...' : '下载视频'}
                </Button>
              </div>
            </div>
          )}

          {/* 使用统计 */}
          {generationResult.usage && (
            <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20">
              <div className="p-2 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-500">
                <Coins className="w-4 h-4 text-white" />
              </div>
              <span className="text-sm font-medium text-foreground">
                {t("cost.consumed", { amount: generationResult.usage.credits_consumed })}
              </span>
            </div>
          )}
        </div>
      )}

      {/* 错误信息 */}
      {generationResult.status === 'failed' && generationResult.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t("errors.generation_failed", { detail: generationResult.error.detail })}
          </AlertDescription>
        </Alert>
      )}

      {/* 进度显示 */}
      {(generationResult.status === 'pending' || generationResult.status === 'running') && (
        <div className="text-center py-4">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p className="text-sm text-gray-600">
            Generating... please wait...
            {generationResult.progress !== undefined && ` (${generationResult.progress}%)`}
          </p>
        </div>
      )}

      {/* 图片预览模态框 */}
      {previewImage && (
        <ImagePreviewModal
          isOpen={!!previewImage}
          onClose={() => setPreviewImage(null)}
          imageUrl={previewImage.url}
          imageAlt={previewImage.alt}
          onDownload={() => handleImageDownload(previewImage.url)}
        />
      )}
    </div>
  );
}
