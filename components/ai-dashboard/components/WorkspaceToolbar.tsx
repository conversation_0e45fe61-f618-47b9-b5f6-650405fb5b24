"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Brain, Minimize, Maximize } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../hooks/use-device-layout";

interface WorkspaceToolbarProps {
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
}

export function WorkspaceToolbar({ isFullscreen, onToggleFullscreen }: WorkspaceToolbarProps) {
  const t = useTranslations("ai-dashboard");
  const tWorkspace = useTranslations("ai-dashboard.workspace");
  const { isMobile, isSmallMobile } = useDeviceLayout();

  if (isFullscreen) {
    // 全屏模式：简洁的工具栏样式
    return (
      <div className={`sticky top-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/50 shadow-sm ${
        isMobile ? 'px-0 py-1' : 'px-6 py-4'
      }`}>
        <div className={`flex items-center justify-between ${
          isMobile ? 'px-2' : ''
        }`}>
          <div className="flex items-center gap-2 md:gap-4">
            <div className={`rounded-xl bg-gradient-to-r from-primary to-accent ${
              isMobile ? 'p-1.5' : 'p-2'
            }`}>
              <Brain className={`text-primary-foreground ${
                isMobile ? 'w-4 h-4' : 'w-6 h-6'
              }`} />
            </div>
            <h1 className={`font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${
              isMobile ? 'text-sm' : 'text-xl'
            }`}>
              {isMobile ? tWorkspace("title") : `${tWorkspace("title")} - ${t("workspace.fullscreen")}`}
            </h1>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onToggleFullscreen}
            className="flex items-center gap-1 md:gap-2 z-50 relative"
          >
            <Minimize className="w-4 h-4" />
            {!isMobile && t("toolbar.exit_fullscreen")}
          </Button>
        </div>
      </div>
    );
  }

  // 常规模式：丰富的头部样式
  return (
    <div className={`bg-gradient-to-br from-background/50 via-background to-accent/5 backdrop-blur-sm ${
      isSmallMobile ? 'px-3 py-4' :
      isMobile ? 'px-4 py-6' : 'px-6 py-12'
    }`}>
      <div className={`flex items-center justify-between ${
        isSmallMobile ? 'mb-3' :
        isMobile ? 'mb-4' : 'mb-8'
      }`}>
        <div className={`flex items-center ${
          isSmallMobile ? 'gap-2' :
          isMobile ? 'gap-3' : 'gap-4'
        }`}>
          <div className={`rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg ${
            isSmallMobile ? 'p-1.5' :
            isMobile ? 'p-2' : 'p-3'
          }`}>
            <Brain className={`text-primary-foreground ${
              isSmallMobile ? 'w-5 h-5' :
              isMobile ? 'w-6 h-6' : 'w-8 h-8'
            }`} />
          </div>
          <div>
            <h1 className={`font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${
              isSmallMobile ? 'text-lg' :
              isMobile ? 'text-xl' : 'text-3xl'
            }`}>
              {tWorkspace("title")}
            </h1>
            {!isSmallMobile && (
              <p className={`text-muted-foreground ${
                isMobile ? 'mt-0.5 text-sm' : 'mt-1 text-base'
              }`}>{tWorkspace("subtitle")}</p>
            )}
          </div>
        </div>
        <Button
          variant="outline"
          size={isSmallMobile ? "sm" : isMobile ? "default" : "lg"}
          onClick={onToggleFullscreen}
          className={`flex items-center ${isSmallMobile ? 'gap-1' : 'gap-2'}`}
        >
          <Maximize className={`${
            isSmallMobile ? 'w-3 h-3' :
            isMobile ? 'w-4 h-4' : 'w-5 h-5'
          }`} />
          {!isSmallMobile && tWorkspace("fullscreen")}
        </Button>
      </div>
    </div>
  );
}
