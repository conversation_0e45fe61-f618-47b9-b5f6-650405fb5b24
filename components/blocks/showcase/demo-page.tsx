"use client";

import { PromptShowcase, ComparisonShowcase } from "./index";
import { PromptShowcaseProps, ComparisonShowcaseProps } from "@/types/blocks/showcase";

export default function ShowcaseDemoPage() {
  return (
    <div className="space-y-24 py-8">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Showcase 组件演示</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          展示 PromptShowcase 和 ComparisonShowcase 组件的轮播功能和布局选项
        </p>
      </div>

      {/* 基础 Prompt Showcase */}
      {/* <PromptShowcaseBasic /> */}

      {/* 50% 宽度轮播 Prompt Showcase */}
      <PromptShowcase50Carousel />

      {/* 100% 宽度轮播 Prompt Showcase */}
      <PromptShowcase100Carousel />

      {/* 水平布局 Prompt Showcase */}
      <PromptShowcaseHorizontal />

      {/* 基础 Comparison Showcase */}
      {/* <ComparisonShowcaseBasic /> */}

      {/* 50% 宽度轮播 Comparison Showcase */}
      <ComparisonShowcase50Carousel />

      {/* 100% 宽度轮播 Comparison Showcase */}
      <ComparisonShowcase100Carousel />
    </div>
  );
}

// 基础 Prompt Showcase
function PromptShowcaseBasic() {
  const promptShowcaseData: PromptShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 创作 Prompt 精选 PromptShowcaseBasic",
      description: "精心调试的高质量 Prompt，助您在 FLUX 和 KREA 平台创作出令人惊艳的 AI 作品",
      items: [
        {
          title: "超现实主义人像",
          description: "创造具有梦幻色彩和超现实元素的人物肖像",
          prompt: "Surreal portrait of a person with flowing liquid hair that transforms into cosmic nebula, ethereal lighting, dreamlike atmosphere, hyperrealistic details, vibrant colors blending into abstract patterns, professional photography style, 8K ultra-detailed",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-1.webp",
            alt: "超现实主义人像作品"
          },
          category: "人像创作",
          tags: ["超现实", "梦幻", "宇宙", "液体"]
        },
        {
          title: "科幻场景设计",
          prompt: "Futuristic sci-fi environment with holographic interfaces, neon-lit corridors, advanced technology panels, atmospheric fog, dramatic lighting with blue and purple tones, cyberpunk aesthetic, highly detailed architecture, cinematic composition",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-2.webp",
            alt: "科幻场景设计作品"
          },
          category: "科幻设计",
          tags: ["科幻", "全息", "霓虹", "赛博朋克"]
        },
        {
          title: "抽象艺术创作",
          description: "充满创意的抽象艺术风格图像",
          prompt: "Abstract digital art with flowing geometric shapes, gradient colors transitioning from warm to cool tones, dynamic composition with depth and movement, modern artistic style, high contrast, professional digital artwork, 4K resolution",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-3.webp",
            alt: "抽象艺术创作作品"
          },
          category: "抽象艺术",
          tags: ["抽象", "几何", "渐变", "动态"]
        }
      ]
    }
  };

  return <PromptShowcase {...promptShowcaseData} />;
}

// 轮播 Prompt Showcase
function PromptShowcaseCarousel() {
  const promptShowcaseData: PromptShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 创作 Prompt 轮播展示 PromptShowcaseCarousel",
      description: "自动轮播的高质量 Prompt 集合，支持手动控制",
      items: [
        {
          title: "超现实主义人像",
          description: "创造具有梦幻色彩和超现实元素的人物肖像",
          prompt: "Surreal portrait of a person with flowing liquid hair that transforms into cosmic nebula, ethereal lighting, dreamlike atmosphere, hyperrealistic details, vibrant colors blending into abstract patterns, professional photography style, 8K ultra-detailed",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-1.webp",
            alt: "超现实主义人像作品"
          },
          category: "人像创作",
          tags: ["超现实", "梦幻", "宇宙", "液体"]
        },
        {
          title: "科幻场景设计（单张图片）",
          prompt: "Futuristic sci-fi environment with holographic interfaces, neon-lit corridors, advanced technology panels, atmospheric fog, dramatic lighting with blue and purple tones, cyberpunk aesthetic, highly detailed architecture, cinematic composition",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-2.webp",
            alt: "科幻场景设计作品"
          },
          category: "科幻设计",
          tags: ["科幻", "全息", "霓虹", "赛博朋克"]
        }
      ]
    },
    cardWidth: '50%',
    autoplayDelay: 4000
  };

  return <PromptShowcase {...promptShowcaseData} />;
}

// 水平布局 Prompt Showcase
function PromptShowcaseHorizontal() {
  const promptShowcaseData: PromptShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 水平布局展示 PromptShowcaseHorizontal",
      description: "图片与文字水平排列的展示方式",
      items: [
        {
          title: "超现实主义人像",
          description: "创造具有梦幻色彩和超现实元素的人物肖像",
          prompt: "Surreal portrait of a person with flowing liquid hair that transforms into cosmic nebula, ethereal lighting, dreamlike atmosphere, hyperrealistic details, vibrant colors blending into abstract patterns, professional photography style, 8K ultra-detailed",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-1.webp",
            alt: "超现实主义人像作品"
          },
          category: "人像创作",
          tags: ["超现实", "梦幻", "宇宙", "液体"]
        },
        {
          title: "科幻场景设计",
          prompt: "Futuristic sci-fi environment with holographic interfaces, neon-lit corridors, advanced technology panels, atmospheric fog, dramatic lighting with blue and purple tones, cyberpunk aesthetic, highly detailed architecture, cinematic composition",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-2.webp",
            alt: "科幻场景设计作品"
          },
          category: "科幻设计",
          tags: ["科幻", "全息", "霓虹", "赛博朋克"]
        }
      ]
    },
    layout: 'horizontal',
    imagePosition: 'left'
  };

  return <PromptShowcase {...promptShowcaseData} />;
}

// 基础 Comparison Showcase
function ComparisonShowcaseBasic() {
  const comparisonShowcaseData: ComparisonShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 图像处理前后对比 ComparisonShowcaseBasic",
      description: "展示 FLUX 和 KREA AI 强大的图像处理能力，从普通照片到专业级作品的华丽转变",
      items: [
        {
          title: "艺术风格转换效果",
          description: "将普通图像转换为具有艺术感的创作作品",
          prompt: "Transform this image with artistic enhancement: apply cinematic lighting, enhance colors and contrast, add artistic depth and atmosphere, maintain natural proportions while creating a more visually striking and professional appearance. Style: photorealistic with artistic flair, high detail, dramatic lighting.",
          beforeImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-1.jpg",
            alt: "AI处理前的原始图像"
          },
          afterImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-2.jpg",
            alt: "AI艺术风格转换后的图像"
          },
          category: "艺术转换",
          tags: ["风格转换", "艺术增强", "色彩优化", "专业级"]
        }
      ]
    }
  };

  return <ComparisonShowcase {...comparisonShowcaseData} />;
}

// 轮播 Comparison Showcase
function ComparisonShowcaseCarousel() {
  const comparisonShowcaseData: ComparisonShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 图像处理轮播对比 ComparisonShowcaseCarousel",
      description: "自动轮播的图像处理效果对比展示",
      items: [
        {
          title: "艺术风格转换效果",
          description: "将普通图像转换为具有艺术感的创作作品",
          prompt: "Transform this image with artistic enhancement: apply cinematic lighting, enhance colors and contrast, add artistic depth and atmosphere, maintain natural proportions while creating a more visually striking and professional appearance. Style: photorealistic with artistic flair, high detail, dramatic lighting.",
          beforeImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-1.jpg",
            alt: "AI处理前的原始图像"
          },
          afterImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-2.jpg",
            alt: "AI艺术风格转换后的图像"
          },
          category: "艺术转换",
          tags: ["风格转换", "艺术增强", "色彩优化", "专业级"]
        }
      ]
    },
    cardWidth: '100%',
    autoplayDelay: 5000
  };

  return <ComparisonShowcase {...comparisonShowcaseData} />;
}

// 50% 宽度轮播 Prompt Showcase
function PromptShowcase50Carousel() {
  const promptShowcaseData: PromptShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 创作 Prompt 50% 宽度轮播 PromptShowcase50Carousel",
      description: "50% 宽度的卡片轮播展示",
      items: [
        {
          prompt: "Surreal portrait of a person with flowing liquid hair that transforms into cosmic nebula, ethereal lighting, dreamlike atmosphere, hyperrealistic details, vibrant colors blending into abstract patterns, professional photography style, 8K ultra-detailed",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-1.webp",
            alt: "超现实主义人像作品"
          },
        },
        {
          prompt: "Futuristic sci-fi environment with holographic interfaces, neon-lit corridors, advanced technology panels, atmospheric fog, dramatic lighting with blue and purple tones, cyberpunk aesthetic, highly detailed architecture, cinematic composition",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-2.webp",
            alt: "科幻场景设计作品"
          },
        },
        {
          prompt: "Abstract digital art with flowing geometric shapes, gradient colors transitioning from warm to cool tones, dynamic composition with depth and movement, modern artistic style, high contrast, professional digital artwork, 4K resolution",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-3.webp",
            alt: "抽象艺术创作作品"
          },
        }
      ]
    },
    enableCarousel: true,
    cardWidth: '50%',
    autoplayDelay: 4000
  };

  return <PromptShowcase {...promptShowcaseData} />;
}

// 100% 宽度轮播 Prompt Showcase
function PromptShowcase100Carousel() {
  const promptShowcaseData: PromptShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 创作 Prompt 100% 宽度轮播 PromptShowcase100Carousel",
      description: "100% 宽度的卡片轮播展示",
      items: [
        {
          title: "超现实主义人像",
          description: "创造具有梦幻色彩和超现实元素的人物肖像",
          prompt: "Surreal portrait of a person with flowing liquid hair that transforms into cosmic nebula, ethereal lighting, dreamlike atmosphere, hyperrealistic details, vibrant colors blending into abstract patterns, professional photography style, 8K ultra-detailed",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-1.webp",
            alt: "超现实主义人像作品"
          },
          category: "人像创作",
          tags: ["超现实", "梦幻", "宇宙", "液体"]
        },
        {
          title: "科幻场景设计",
          prompt: "Futuristic sci-fi environment with holographic interfaces, neon-lit corridors, advanced technology panels, atmospheric fog, dramatic lighting with blue and purple tones, cyberpunk aesthetic, highly detailed architecture, cinematic composition",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-2.webp",
            alt: "科幻场景设计作品"
          },
          category: "科幻设计",
          tags: ["科幻", "全息", "霓虹", "赛博朋克"]
        }
      ]
    },
    enableCarousel: true,
    cardWidth: '100%',
    autoplayDelay: 5000
  };

  return <PromptShowcase {...promptShowcaseData} />;
}

// 50% 宽度轮播 Comparison Showcase
function ComparisonShowcase50Carousel() {
  const comparisonShowcaseData: ComparisonShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 图像处理 50% 宽度轮播对比 ComparisonShowcase50Carousel",
      description: "50% 宽度的对比卡片轮播展示",
      items: [
        {
          title: "艺术风格转换效果",
          description: "将普通图像转换为具有艺术感的创作作品",
          prompt: "Transform this image with artistic enhancement: apply cinematic lighting, enhance colors and contrast, add artistic depth and atmosphere, maintain natural proportions while creating a more visually striking and professional appearance. Style: photorealistic with artistic flair, high detail, dramatic lighting.",
          beforeImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-1.jpg",
            alt: "AI处理前的原始图像"
          },
          afterImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-2.jpg",
            alt: "AI艺术风格转换后的图像"
          },
          category: "艺术转换",
          tags: ["风格转换", "艺术增强", "色彩优化", "专业级"]
        },
        {
          title: "人像美化处理",
          description: "AI 智能美化，保持自然效果的同时提升照片质量",
          prompt: "Please enhance this portrait photo with natural beauty effects. Focus on: skin smoothing while maintaining texture, eye brightening, color correction, overall lighting improvement. Keep the result natural and avoid over-processing.",
          beforeImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-1.jpg",
            alt: "处理前的人像照片"
          },
          afterImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-2.jpg",
            alt: "AI美化后的人像照片"
          },
          category: "人像处理",
          tags: ["美颜", "人像", "自然效果"]
        }
      ]
    },
    enableCarousel: true,
    cardWidth: '50%',
    autoplayDelay: 3000
  };

  return <ComparisonShowcase {...comparisonShowcaseData} />;
}

// 100% 宽度轮播 Comparison Showcase
function ComparisonShowcase100Carousel() {
  const comparisonShowcaseData: ComparisonShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 图像处理 100% 宽度轮播对比 ComparisonShowcase100Carousel",
      description: "100% 宽度的对比卡片轮播展示",
      items: [
        {
          title: "艺术风格转换效果",
          description: "将普通图像转换为具有艺术感的创作作品",
          prompt: "Transform this image with artistic enhancement: apply cinematic lighting, enhance colors and contrast, add artistic depth and atmosphere, maintain natural proportions while creating a more visually striking and professional appearance. Style: photorealistic with artistic flair, high detail, dramatic lighting.",
          beforeImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-1.jpg",
            alt: "AI处理前的原始图像"
          },
          afterImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-2.jpg",
            alt: "AI艺术风格转换后的图像"
          },
          category: "艺术转换",
          tags: ["风格转换", "艺术增强", "色彩优化", "专业级"]
        }
      ]
    },
    enableCarousel: true,
    cardWidth: '100%',
    autoplayDelay: 600
  };

  return <ComparisonShowcase {...comparisonShowcaseData} />;
}
