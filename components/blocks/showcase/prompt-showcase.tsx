"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { Co<PERSON>, Check, ChevronDown, ChevronUp } from "lucide-react";
import Image from "next/image";
import { PromptShowcaseProps } from "@/types/blocks/showcase";


export default function PromptShowcase({
  section,
  enableCarousel = false,
  layout = 'vertical',
  imagePosition = 'left',
  cardWidth = 'auto',
  autoplayDelay = 0
}: PromptShowcaseProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [expandedPrompts, setExpandedPrompts] = useState<Set<number>>(new Set());

  // 卡片轮播设置（淡入淡出）
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const totalCards = section.items?.length || 0;

  // 自动播放
  useEffect(() => {
    if (enableCarousel && autoplayDelay > 0 && totalCards > 1) {
      const interval = setInterval(() => {
        setCurrentCardIndex((prev) => (prev + 1) % totalCards);
      }, autoplayDelay);
      return () => clearInterval(interval);
    }
  }, [enableCarousel, autoplayDelay, totalCards]);

  if (section.disabled) {
    return null;
  }



  const handleCopyPrompt = async (prompt: string, index: number) => {
    try {
      await navigator.clipboard.writeText(prompt);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy prompt:', err);
    }
  };

  const togglePrompt = (index: number) => {
    const newExpanded = new Set(expandedPrompts);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedPrompts(newExpanded);
  };

  // 渲染单个卡片的函数
  const renderCard = (item: any, index: number) => {
    if (layout === 'horizontal') {
      // 水平布局
      return (
        <Card
          key={index}
          className="overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col h-full"
        >
          <div className={`flex flex-col md:flex-row ${imagePosition === 'right' ? 'md:flex-row-reverse' : ''} h-full`}>
            {/* 图片区域 */}
            {item.image && (
              <div className="relative aspect-[4/3] md:aspect-[3/4] w-full md:w-1/2 overflow-hidden">
                <Image
                  src={item.image.src}
                  alt={item.image.alt || item.title}
                  fill
                  className="object-cover transition-transform duration-1000 hover:scale-105"
                />
                {item.category && (
                  <div className="absolute top-3 right-3">
                    <Badge variant="outline" className="bg-background/80 backdrop-blur-sm">
                      {item.category}
                    </Badge>
                  </div>
                )}
              </div>
            )}

            {/* 内容区域 */}
            <div className="flex-1 flex flex-col ">
             {item.title && <CardHeader className="pb-0">
                <CardTitle className="text-lg font-semibold line-clamp-2">
                  {item.title}
                </CardTitle>
                {item.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                    {item.description}
                  </p>
                )}
              </CardHeader>
    }

              <CardContent className="flex-1 flex flex-col space-y-3 ">
                {/* 标签区域 */}
                {item.tags && item.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {item.tags.slice(0, 3).map((tag: string, tagIndex: number) => (
                      <Badge
                        key={tagIndex}
                        variant="secondary"
                        className="text-xs px-2 py-0.5"
                      >
                        {tag}
                      </Badge>
                    ))}
                    {item.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs px-2 py-0.5">
                        +{item.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                {/* Prompt 展开/收起区域 */}
                <div className="flex-1">
                  {expandedPrompts.has(index) && (
                    <div className="mb-3">
                      <div className="bg-muted/50 rounded-lg p-3 border border-border/50">
                        <p className="text-xs font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words">
                          {item.prompt}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* 按钮区域 */}
                <div className="space-y-2">
                  <Button
                    onClick={() => togglePrompt(index)}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    {expandedPrompts.has(index) ? (
                      <>
                        <ChevronUp className="w-4 h-4 mr-2" />
                        隐藏 Prompt
                      </>
                    ) : (
                      <>
                        <ChevronDown className="w-4 h-4 mr-2" />
                        显示 Prompt
                      </>
                    )}
                  </Button>

                  {expandedPrompts.has(index) && (
                    <Button
                      onClick={() => handleCopyPrompt(item.prompt, index)}
                      variant="default"
                      size="sm"
                      className="w-full transition-all duration-200"
                    >
                      {copiedIndex === index ? (
                        <>
                          <Check className="w-4 h-4 mr-2" />
                          已复制
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4 mr-2" />
                          复制 Prompt
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </CardContent>
            </div>
          </div>
        </Card>
      );
    } else {
      // 垂直布局（原有布局）
      return (
        <Card
          key={index}
          className="overflow-hidden gap-2 transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col h-full"
        >
          {/* 主要图片区域 */}
          {item.image && (
            <div className="relative aspect-[4/3] w-full overflow-hidden">
              <Image
                src={item.image.src}
                alt={item.image.alt || item.title}
                fill
                className="object-cover transition-transform duration-1000 hover:scale-100"
              />
              {item.category && (
                <div className="absolute top-3 right-3">
                  <Badge variant="outline" className="bg-background/80 backdrop-blur-sm">
                    {item.category}
                  </Badge>
                </div>
              )}
            </div>
          )}


{item.title &&
          <CardHeader className="pb-0">
            <CardTitle className="text-lg font-semibold line-clamp-2">
              {item.title}
            </CardTitle>
            {item.description && (
              <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                {item.description}
              </p>
            )}
          </CardHeader>
    }

          <CardContent className="flex-1 flex flex-col space-y-3">
            {/* 标签区域 */}
            {item.tags && item.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {item.tags.slice(0, 3).map((tag: string, tagIndex: number) => (
                  <Badge
                    key={tagIndex}
                    variant="secondary"
                    className="text-xs px-2 py-0.5"
                  >
                    {tag}
                  </Badge>
                ))}
                {item.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs px-2 py-0.5">
                    +{item.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}

            {/* Prompt 展开/收起区域 */}
            <div className="flex-1">
              {expandedPrompts.has(index) && (
                <div className="mb-3">
                  <div className="bg-muted/50 rounded-lg p-3 border border-border/50">
                    <p className="text-xs font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words">
                      {item.prompt}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* 按钮区域 */}
            <div className="space-y-2">
              <Button
                onClick={() => togglePrompt(index)}
                variant="outline"
                size="sm"
                className="w-full transition-all duration-200"
              >
                {expandedPrompts.has(index) ? (
                  <>
                    <ChevronUp className="w-4 h-4 mr-2" />
                    隐藏 Prompt
                  </>
                ) : (
                  <>
                    <ChevronDown className="w-4 h-4 mr-2" />
                    显示 Prompt
                  </>
                )}
              </Button>

              {expandedPrompts.has(index) && (
                <Button
                  onClick={() => handleCopyPrompt(item.prompt, index)}
                  variant="default"
                  size="sm"
                  className="w-full transition-all duration-200"
                >
                  {copiedIndex === index ? (
                    <>
                      <Check className="w-4 h-4 mr-2" />
                      已复制
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4 mr-2" />
                      复制 Prompt
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      );
    }
  };

  return (
    <section className="container py-16">
      <div className="mx-auto mb-12 text-center">
        <h2 className="mb-6 text-pretty text-3xl font-bold lg:text-4xl">
          {section.title}
        </h2>
        <p className="mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg">
          {section.description}
        </p>
      </div>

      {enableCarousel ? (
        // 卡片轮播模式（淡入淡出）
        <div className="relative">
          <div className="relative min-h-[600px]">
            {section.items?.map((item, index) => (
              <div
                key={index}
                className={`absolute inset-0 transition-opacity duration-300 ${
                  index === currentCardIndex ? 'opacity-100' : 'opacity-0'
                }`}
              >
                <div className={`relative ${
                  cardWidth === '50%'
                    ? 'max-w-[50%] mx-auto'
                    : cardWidth === '100%'
                    ? 'w-full'
                    : 'max-w-4xl mx-auto'
                }`}>
                  {renderCard(item, index)}

                  {/* 轮播指示器 - 在卡片内部底部 */}
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1">
                    {section.items?.map((_, dotIndex) => (
                      <button
                        key={dotIndex}
                        className={`w-2 h-2 rounded-full transition-all ${
                          dotIndex === currentCardIndex ? 'bg-primary' : 'bg-muted-foreground/30'
                        }`}
                        onClick={() => setCurrentCardIndex(dotIndex)}
                      />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        // 网格布局
        <div className={`grid gap-6 ${
          cardWidth === '50%'
            ? 'grid-cols-1 md:grid-cols-2'
            : cardWidth === '100%'
            ? 'grid-cols-1'
            : layout === 'horizontal'
            ? 'grid-cols-1 lg:grid-cols-2'
            : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        }`}>
          {section.items?.map((item, index) => renderCard(item, index))}
        </div>
      )}
    </section>
  );
}
